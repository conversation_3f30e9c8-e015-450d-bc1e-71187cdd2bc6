<template>
  <!-- <pre>
    {{ JSON.stringify($props).length }}
  </pre> -->
  <div class="my-process-designer">
    <div class="my-process-designer__container">
      <div
        class="my-process-designer__canvas"
         :style="{ height: isMobileDevice ? '100%' : '700px' }"
        ref="bpmnCanvas"
      ></div>
    </div>
    <div class="zoom-controls" v-if="!isMobileDevice">
      <el-button size="small" @click="processZoomIn()" class="zoom-btn naive-style">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="zoom-icon"
        >
          <circle cx="11" cy="11" r="8"></circle>
          <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
          <line x1="11" y1="8" x2="11" y2="14"></line>
          <line x1="8" y1="11" x2="14" y2="11"></line>
        </svg>
        放大
      </el-button>
      <el-button size="small" class="zoom-btn zoom-value naive-style">{{
        Math.floor(defaultZoom * 100) + '%'
      }}</el-button>
      <el-button size="small" @click="processZoomOut()" class="zoom-btn naive-style">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="zoom-icon"
        >
          <circle cx="11" cy="11" r="8"></circle>
          <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
          <line x1="8" y1="11" x2="14" y2="11"></line>
        </svg>
        缩小
      </el-button>
      <el-button type="default" size="small" @click="processReZoom()" class="zoom-btn naive-style">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="zoom-icon"
        >
          <polyline points="1 4 1 10 7 10"></polyline>
          <polyline points="23 20 23 14 17 14"></polyline>
          <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
        </svg>
        重置
      </el-button>
      <el-button type="success" size="small" @click="downloadDiagram()" class="zoom-btn naive-style">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="zoom-icon"
        >
          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
          <polyline points="7 10 12 15 17 10"></polyline>
          <line x1="12" y1="15" x2="12" y2="3"></line>
        </svg>
        下载图片
      </el-button>
    </div>

    <!-- 多签类型图例说明 -->
    <div v-if="showMultiSignLegend" class="multi-sign-legend">
      <div class="legend-header">
        <div class="legend-title">多签节点说明：</div>
        <div class="legend-close" @click="closeMultiSignLegend">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </div>
      </div>
      <div class="legend-item sequential">
        <div class="legend-icon"></div>
        <div class="legend-text">串行多签：按顺序依次审批</div>
      </div>
      <div class="legend-item and">
        <div class="legend-icon"></div>
        <div class="legend-text">会签：所有人都需要审批通过</div>
      </div>
      <div class="legend-item or">
        <div class="legend-icon"></div>
        <div class="legend-text">或签：任一人审批即可</div>
      </div>
      <div class="legend-item parallel">
        <div class="legend-icon"></div>
        <div class="legend-text">并行多签：同时处理</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import BpmnViewer from 'bpmn-js/lib/Viewer'
  import DefaultEmptyXML from './plugins/defaultEmpty'
  import { getIntDictOptions, DICT_TYPE } from '@/utils/bpmAdapter/bpmDictAdapter'
  import { formatDate } from '@/utils/bpmAdapter/formatTime'
  import { isEmpty } from '@/utils/bpmAdapter/is'
  import { onBeforeUnmount, onMounted, provide, Ref, ref, toRaw, watch } from 'vue'
  import JPGlobal from '@/types/common/jglobal'
  import { getScreenInfo } from '@/utils/device'

  defineOptions({ name: 'MyProcessViewer' })

  const props = defineProps({
    value: {
      // BPMN XML 字符串
      type: String,
      default: '',
    },
    prefix: {
      // 使用哪个引擎
      type: String,
      default: 'camunda',
    },
    activityData: {
      // 活动的数据。传递时，可高亮流程
      type: Array,
      default: () => [],
    },
    processInstanceData: {
      // 流程实例的数据。传递时，可展示流程发起人等信息
      type: Object,
      default: () => {},
    },
    taskData: {
      // 任务实例的数据。传递时，可展示 UserTask 审核相关的信息
      type: Array,
      default: () => [],
    },
  })

  provide('configGlobal', props)
  watch(props, newVal => {
    console.log('newVal', newVal)
  })
  const isMobileDevice = inject<Ref<Boolean>>('isMobileDevice')

  const emit = defineEmits(['destroy', 'nextTasks'])

  let bpmnModeler

  const xml = ref('')
  const activityLists = ref<any[]>([])
  const processInstance = ref<any>(undefined)
  const taskList = ref<any[]>([])
  const bpmnCanvas = ref()
  // const element = ref()
  const elementOverlayIds = ref<any>(null)
  const overlays = ref<any>(null)

  // 控制多签图例的显示和隐藏
  const showMultiSignLegend = ref(false)

  // 关闭多签图例
  const closeMultiSignLegend = () => {
    showMultiSignLegend.value = false
  }

  const defaultZoom = ref(1)
  onMounted(() => {
    if (isMobileDevice.value) {
      defaultZoom.value = 0.5
    }
  })

  const initBpmnModeler = () => {
    const screenInfo = getScreenInfo()

    if (bpmnModeler) return
    const cfg: any = {
      container: bpmnCanvas.value,
      bpmnRenderer: {},
      width: '100%',
    }
    if (isMobileDevice.value) {
      cfg.height = '100%'
    }
    bpmnModeler = new BpmnViewer(cfg)
  }
  watch(isMobileDevice, newVal => {
    init()
  })
  onMounted(() => {
    initBpmnModeler()
  })

  /* 创建新的流程图 */
  const createNewDiagram = async xml => {
    // 将字符串转换成图显示出来
    let newId = `Process_${new Date().getTime()}`
    let newName = `业务流程_${new Date().getTime()}`
    let xmlString = xml || DefaultEmptyXML(newId, newName, props.prefix)
    try {
      let { warnings } = await bpmnModeler.importXML(xmlString)
      if (warnings && warnings.length) {
        warnings.forEach(warn => console.warn(warn))
      }
      // 高亮流程图
      await highlightDiagram()
      const canvas = bpmnModeler.get('canvas')
      canvas.zoom('fit-viewport', 'auto')
    } catch (e) {
      console.error(e)
      // console.error(`[Process Designer Warn]: ${e?.message || e}`);
    }
  }

  /* 高亮流程图 */
  // TODO 芋艿：如果多个 endActivity 的话，目前的逻辑可能有一定的问题。https://www.jdon.com/workflow/multi-events.html
  const highlightDiagram = async () => {
    const activityList = activityLists.value
    if (activityList.length === 0) {
      return
    }
    // 参考自 https://gitee.com/tony2y/RuoYi-flowable/blob/master/ruoyi-ui/src/components/Process/index.vue#L222 实现
    // 再次基础上，增加不同审批结果的颜色等等
    let canvas = bpmnModeler.get('canvas')
    let todoActivity: any = activityList.find((m: any) => !m.endTime) // 找到待办的任务
    let endActivity: any = activityList[activityList.length - 1] // 获得最后一个任务
    let findProcessTask = false //是否已经高亮了进行中的任务
    //进行中高亮之后的任务 key 集合，用于过滤掉 taskList 进行中后面的任务，避免进行中后面的数据 Hover 还有数据
    let removeTaskDefinitionKeyList = []
    // 用于检测是否有多签节点
    let hasMultiSignNode = false
    // debugger
    const flowElements =
      bpmnModeler.getDefinitions().rootElements[0].flowElements ||
      bpmnModeler.getDefinitions().rootElements[0].participants?.[0]?.$parent?.participants?.[0]?.processRef
        .flowElements ||
      []

    // 收集所有待审批任务
    const nextTasks: any[] = []

    flowElements.forEach((n: any) => {
      let activity: any = activityList.find((m: any) => m.key === n.id) // 找到对应的活动

      if (!activity) {
        return
      }
      if (n.$type === 'bpmn:UserTask') {
        // 用户任务
        // 处理用户任务的高亮

        const task: any = taskList.value.find((m: any) => m.id === activity.taskId) // 找到活动对应的 taskId
        if (!task) {
          return
        }

        // 如果是待审批或进行中的任务，收集到nextTasks中
        if (task.status === 0 || task.status === 1) {
          // nextTasks.push({ ...task })
        }

        // 进行中的任务已经高亮过了，则不高亮后面的任务了
        if (findProcessTask) {
          removeTaskDefinitionKeyList.push(n.id)
          canvas.addMarker(n.id, getResultCss(task.status))

          return
        }
        // 高亮任务
        canvas.addMarker(n.id, getResultCss(task.status))

        // 如果是多签任务，添加多签类型标识
        const multiSignClass = getMultiSignTypeClass(n)
        if (multiSignClass) {
          hasMultiSignNode = true // 标记存在多签节点
          canvas.addMarker(n.id, multiSignClass)
        }

        //标记是否高亮了进行中任务
        if (task.status === 1) {
          findProcessTask = true
        }
        // 如果非通过，就不走后面的线条了
        if (task.status !== 2) {
          return
        }
        // 处理 outgoing 出线
        const outgoing = getActivityOutgoing(activity)
        outgoing?.forEach((nn: any) => {
          // debugger
          let targetActivity: any = activityList.find((m: any) => m.key === nn.targetRef.id)
          // 如果目标活动存在，则根据该活动是否结束，进行【bpmn:SequenceFlow】连线的高亮设置
          if (targetActivity) {
            canvas.addMarker(nn.id, targetActivity.endTime ? 'highlight' : 'highlight-todo')
          } else if (nn.targetRef.$type === 'bpmn:ExclusiveGateway') {
            // TODO 芋艿：这个流程，暂时没走到过
            canvas.addMarker(nn.id, activity.endTime ? 'highlight' : 'highlight-todo')
            canvas.addMarker(nn.targetRef.id, activity.endTime ? 'highlight' : 'highlight-todo')
          } else if (nn.targetRef.$type === 'bpmn:EndEvent') {
            // TODO 芋艿：这个流程，暂时没走到过
            if (!todoActivity && endActivity.key === n.id) {
              canvas.addMarker(nn.id, 'highlight')
              canvas.addMarker(nn.targetRef.id, 'highlight')
            }
            if (!activity.endTime) {
              canvas.addMarker(nn.id, 'highlight-todo')
              canvas.addMarker(nn.targetRef.id, 'highlight-todo')
            }
          }
        })
      } else if (n.$type === 'bpmn:ExclusiveGateway') {
        // 排它网关
        // 设置【bpmn:ExclusiveGateway】排它网关的高亮
        canvas.addMarker(n.id, getActivityHighlightCss(activity))
        // 查找需要高亮的连线
        let matchNN: any = undefined
        let matchActivity: any = undefined
        n.outgoing?.forEach((nn: any) => {
          let targetActivity = activityList.find((m: any) => m.key === nn.targetRef.id)
          if (!targetActivity) {
            return
          }
          // 特殊判断 endEvent 类型的原因，ExclusiveGateway 可能后续连有 2 个路径：
          //  1. 一个是 UserTask => EndEvent
          //  2. 一个是 EndEvent
          // 在选择路径 1 时，其实 EndEvent 可能也存在，导致 1 和 2 都高亮，显然是不正确的。
          // 所以，在 matchActivity 为 EndEvent 时，需要进行覆盖~~
          if (!matchActivity || matchActivity.type === 'endEvent') {
            matchNN = nn
            matchActivity = targetActivity
          }
        })
        if (matchNN && matchActivity) {
          canvas.addMarker(matchNN.id, getActivityHighlightCss(matchActivity))
        }
      } else if (n.$type === 'bpmn:ParallelGateway') {
        // 并行网关
        // 设置【bpmn:ParallelGateway】并行网关的高亮
        canvas.addMarker(n.id, getActivityHighlightCss(activity))
        n.outgoing?.forEach((nn: any) => {
          // 获得连线是否有指向目标。如果有，则进行高亮
          const targetActivity = activityList.find((m: any) => m.key === nn.targetRef.id)
          if (targetActivity) {
            canvas.addMarker(nn.id, getActivityHighlightCss(targetActivity)) // 高亮【bpmn:SequenceFlow】连线
            // 高亮【...】目标。其中 ... 可以是 bpm:UserTask、也可以是其它的。当然，如果是 bpm:UserTask 的话，其实不做高亮也没问题，因为上面有逻辑做了这块。
            canvas.addMarker(nn.targetRef.id, getActivityHighlightCss(targetActivity))
          }
        })
      } else if (n.$type === 'bpmn:StartEvent') {
        // 开始节点
        canvas.addMarker(n.id, 'highlight')
        n.outgoing?.forEach(nn => {
          // outgoing 例如说【bpmn:SequenceFlow】连线
          // 获得连线是否有指向目标。如果有，则进行高亮
          let targetActivity = activityList.find((m: any) => m.key === nn.targetRef.id)
          if (targetActivity) {
            canvas.addMarker(nn.id, 'highlight') // 高亮【bpmn:SequenceFlow】连线
            canvas.addMarker(n.id, 'highlight') // 高亮【bpmn:StartEvent】开始节点（自己）
          }
        })
      } else if (n.$type === 'bpmn:EndEvent') {
        // 结束节点
        if (!processInstance.value || processInstance.value.status === 1) {
          return
        }
        canvas.addMarker(n.id, getResultCss(processInstance.value.status))
      } else if (n.$type === 'bpmn:ServiceTask') {
        //服务任务
        if (activity.startTime > 0 && activity.endTime === 0) {
          //进入执行，标识进行色
          canvas.addMarker(n.id, getResultCss(1))
        }
        if (activity.endTime > 0) {
          // 执行完成，节点标识完成色, 所有outgoing标识完成色。
          canvas.addMarker(n.id, getResultCss(2))
          const outgoing = getActivityOutgoing(activity)
          outgoing?.forEach(out => {
            canvas.addMarker(out.id, getResultCss(2))
          })
        }
      } else if (n.$type === 'bpmn:SequenceFlow') {
        let targetActivity = activityList.find((m: any) => m.key === n.targetRef.id)
        if (targetActivity) {
          canvas.addMarker(n.id, getActivityHighlightCss(targetActivity))
        }
      }
    })

    // 设置是否显示多签图例
    showMultiSignLegend.value = hasMultiSignNode

    if (!isEmpty(removeTaskDefinitionKeyList)) {
      //@ts-ignore
      taskList.value = taskList.value.filter(item => !removeTaskDefinitionKeyList.includes(item.taskDefinitionKey))
    }

    // 发送下一审批任务
    emit('nextTasks', nextTasks)
  }

  // 获取节点的高亮样式
  const getActivityHighlightCss = activity => {
    return activity.endTime ? 'highlight' : 'highlight-todo'
  }

  // 判断多签类型并设置对应的css class
  const getMultiSignTypeClass = element => {
    if (element.$type !== 'bpmn:UserTask' || !element.loopCharacteristics) {
      return ''
    }

    // 如果不是多实例循环特性，则不是多签
    if (element.loopCharacteristics.$type !== 'bpmn:MultiInstanceLoopCharacteristics') {
      return ''
    }

    // 是否串行执行
    const isSequential = element.loopCharacteristics.isSequential || false

    // 解析完成条件判断是会签还是或签
    let completionCondition = element.loopCharacteristics.completionCondition?.body || ''
    const isOrSign = completionCondition.includes('nrOfCompletedInstances > 0') // 只要有人完成即可
    const isAndSign = completionCondition.includes('>= nrOfInstances') // 需要所有人完成

    if (isSequential) {
      return 'multi-sequential' // 串行
    } else if (isOrSign) {
      return 'multi-or' // 或签
    } else if (isAndSign) {
      return 'multi-and' // 会签
    } else {
      return 'multi-parallel' // 并行
    }
  }

  const getResultCss = status => {
    if (status === 1) {
      // 审批中
      return 'highlight-todo'
    } else if (status === 2) {
      // 已通过
      return 'highlight'
    } else if (status === 3) {
      // 不通过
      return 'highlight-reject'
    } else if (status === 4) {
      // 已取消
      return 'highlight-cancel'
    } else if (status === 5) {
      // 退回
      return 'highlight-return'
    } else if (status === 6) {
      // 委派
      return 'highlight-todo'
    } else if (status === 7) {
      // 审批通过中
      return 'highlight-todo'
    } else if (status === 0) {
      // 待审批
      return 'highlight-todo'
    }
    return ''
  }

  const getActivityOutgoing = activity => {
    // 如果有 outgoing，则直接使用它
    if (activity.outgoing && activity.outgoing.length > 0) {
      return activity.outgoing
    }
    // 如果没有，则遍历获得起点为它的【bpmn:SequenceFlow】节点们。原因是：bpmn-js 的 UserTask 拿不到 outgoing
    const flowElements = bpmnModeler.getDefinitions().rootElements[0].flowElements
    const outgoing: any[] = []
    flowElements?.forEach((item: any) => {
      if (item.$type !== 'bpmn:SequenceFlow') {
        return
      }
      if (item.sourceRef.id === activity.key) {
        outgoing.push(item)
      }
    })
    return outgoing
  }
  const initModelListeners = () => {
    const EventBus = bpmnModeler.get('eventBus')
    // 注册需要的监听事件
    EventBus.on('element.hover', function (eventObj) {
      let element = eventObj ? eventObj.element : null
      elementHover(element)
    })

    // let elementId: string;
    // EventBus.on("element.hover", (event) => {
    //   // 移出时摧毁上一个
    //   if (elementId && event.element.id != elementId) {
    //     popper?.destroy();
    //     open.value = false;
    //   }
    //   // 移入的如果是用户节点，就弹出框
    //   if (event.element.type === "bpmn:UserTask") {
    //     showNodeInfo(event.element.id);
    //   }
    //   elementId = event.element.id;
    // });

    EventBus.on('element.out', function (eventObj) {
      let element = eventObj ? eventObj.element : null
      elementOut(element)
    })
  }

  // /**
  //  * 弹出节点信息
  //  * @param elementId 节点id
  //  */
  //  const showNodeInfo = (elementId: string) => {
  //   nodeInfoItem.value = pops.highlightNode.nodeInfo.find((t: any) => t.activityId === elementId)?.historyRecordVo;
  //   if (!nodeInfoItem.value) return;

  //   const element = document.querySelector(`[data-element-id='${elementId}']`) as HTMLElement;
  //   const nodeInfo = document.querySelector(`#nodeInfo`) as HTMLElement;
  //   open.value = true;
  //   popper = new Popper(element, nodeInfo, {
  //     placement: "bottom"
  //   });
  // };

  // 流程图的元素被 hover
  const elementHover = element => {
    element.value = element
    if (!elementOverlayIds.value) {
      elementOverlayIds.value = {}
    }

    if (!overlays.value) {
      overlays.value = bpmnModeler.get('overlays')
    }
    // 展示信息
    console.log(activityLists.value, 'activityLists.value')
    console.log(element.value, 'element.value')
    const activity = activityLists.value.find(m => m.key === element.value.id)
    // console.log(activity, 'activityactivityactivityactivity')
    // debugger

    if (!activity) {
      return
    }
    // !elementOverlayIds.value[element.value.id] &&
    if (element.value.type !== 'bpmn:Process') {
      let html = ''

      if (element.value.type === 'bpmn:StartEvent' && processInstance.value) {
        html = `<p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg> 发起人：${
          processInstance?.value?.startUser?.empName || ''
        }${
          processInstance?.value?.startUser?.empCode ? ' (' + processInstance?.value?.startUser?.empCode + ')' : ''
        }</p>
                    <p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 3h18v18H3zM8 12h8"></path><path d="M12 8v8"></path></svg> 部门：${
                      processInstance?.value?.startUser?.deptName || ''
                    }</p>
                    <p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg> 创建时间：${formatDate(
                      processInstance?.value?.createTime
                    )}</p>`
      } else if (element.value.type === 'bpmn:UserTask') {
        // 找到该节点对应的所有任务（多签情况下会有多个）
        let tasks = taskList.value.filter(m => m.taskDefinitionKey === element.value.id)
        if (!tasks || tasks.length === 0) {
          return
        }

        // 判断多签类型
        let multiSignType = ''
        let multiSignLabel = ''

        // 获取节点定义，分析是否为多签以及多签类型
        const flowElements = bpmnModeler.getDefinitions().rootElements[0].flowElements
        const nodeDefinition = flowElements.find(item => item.id === element.value.id)

        if (nodeDefinition && nodeDefinition.loopCharacteristics) {
          // 是否有多实例循环特性，即是否为多签
          const isMultiInstance = nodeDefinition.loopCharacteristics.$type === 'bpmn:MultiInstanceLoopCharacteristics'

          if (isMultiInstance) {
            // 是否串行执行
            const isSequential = nodeDefinition.loopCharacteristics.isSequential || false

            // 解析完成条件判断是会签还是或签
            let completionCondition = nodeDefinition.loopCharacteristics.completionCondition?.body || ''
            const isOrSign = completionCondition.includes('nrOfCompletedInstances > 0') // 只要有人完成即可
            const isAndSign = completionCondition.includes('>= nrOfInstances') // 需要所有人完成

            if (isSequential) {
              multiSignType = 'sequential'
              multiSignLabel = '串行多签'
            } else if (isOrSign) {
              multiSignType = 'or'
              multiSignLabel = '或签（任一人审批即可）'
            } else if (isAndSign) {
              multiSignType = 'and'
              multiSignLabel = '会签（需所有人审批）'
            } else {
              multiSignType = 'parallel'
              multiSignLabel = '并行多签'
            }
          }
        }

        // 如果是多签节点，添加多签类型标识和进度
        if (multiSignType) {
          html += `<div class="multi-sign-type ${multiSignType}">
              <p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line></svg>
              ${multiSignLabel} | 进度: ${tasks.filter(t => t.status !== 1).length}/${tasks.length}</p>
            </div>`
        }

        // 遍历所有任务，为每个审批人生成展示信息
        tasks.forEach((task, index) => {
          let optionData = getIntDictOptions(DICT_TYPE.BPM_TASK_STATUS)
          let dataResult = ''
          optionData.forEach(element => {
            if (element.value == task.status) {
              dataResult = element.label
            }
          })

          // 如果有多个审批人，添加分隔线
          if (index > 0 && tasks.length > 1) {
            html += `<div style="border-top: 1px dashed #ddd; margin: 4px 0;"></div>`
          }

          html += `<p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg> 审批人：${
            task?.assigneeUser?.empName || ''
          }</p>
            <p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 3h18v18H3zM8 12h8"></path><path d="M12 8v8"></path></svg> 部门：${
              task?.assigneeUser?.deptName || ''
            }</p>
            <p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg> 结果：${dataResult}</p>
            <p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg> 创建时间：${formatDate(
              task.createTime
            )}</p>`

          if (task.endTime) {
            html += `<p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="8 12 12 16 16 12"></polyline><line x1="12" y1="8" x2="12" y2="16"></line></svg> 结束时间：${formatDate(
              task.endTime
            )}</p>`
          }

          if (task.reason) {
            html += `<p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg> 审批建议：${task.reason}</p>`
          }

          if (task.needSign && task.status != 1) {
            html += `
              <div style="margin-top: 10px; font-weight: normal">
                <span style="font-weight: normal">
                  <svg xmlns="http://www.w3.org/2000/svg" class="preview-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M15.5 3H5a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h14a2 2 0 0 0 2-2V8.5L15.5 3Z"></path><path d="M15 3v6h6"></path></svg>
                  签名：
                </span>
                <img width="60" src="${JPGlobal.getRealOCUrl(task.signUrl)}" />
              </div>
            `
          }

          if (task.needAttachment && task.status != 1 && task.attachmentList && task.attachmentList.length > 0) {
            html += `
              <div style="margin-top: 10px; font-weight: normal">
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" class="preview-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>
                  附件：
                </span>
                <ul>
                  ${task.attachmentList.map(attachment => `<li>${attachment.label}</li>`).join('')}
                </ul>
              </div>
            `
          }
        })
      } else if (element.value.type === 'bpmn:ServiceTask' && processInstance.value) {
        if (activity.startTime > 0) {
          html = `<p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg> 创建时间：${formatDate(
            activity.startTime
          )}</p>`
        }
        if (activity.endTime > 0) {
          html += `<p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="8 12 12 16 16 12"></polyline><line x1="12" y1="8" x2="12" y2="16"></line></svg> 结束时间：${formatDate(
            activity.endTime
          )}</p>`
        }
        console.log(html)
      } else if (element.value.type === 'bpmn:EndEvent' && processInstance.value) {
        let optionData = getIntDictOptions(DICT_TYPE.BPM_TASK_STATUS)
        let dataResult = ''
        optionData.forEach(element => {
          if (element.value == processInstance.value.status) {
            dataResult = element.label
          }
        })
        html = `<p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg> 结果：${dataResult}</p>`
        if (processInstance.value.endTime) {
          html += `<p><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="8 12 12 16 16 12"></polyline><line x1="12" y1="8" x2="12" y2="16"></line></svg> 结束时间：${formatDate(
            processInstance.value.endTime
          )}</p>`
        }
      }

      // 添加节点ID，移到最下面
      html += `<p class="node-id"><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line></svg> 节点ID: ${element.value.id}</p>`

      elementOverlayIds.value[element.value.id] = toRaw(overlays.value)?.add(element.value, {
        position: { left: 0, bottom: 0 },
        html: `<div class="element-overlays">${html}</div>`,
      })
    } else {
      debugger
    }
  }

  // 流程图的元素被 out
  const elementOut = element => {
    toRaw(overlays.value).remove({ element })
    elementOverlayIds.value[element.id] = null
  }

  // 缩放方法
  const processZoomIn = (zoomStep = 0.1) => {
    init()
    let newZoom = Math.floor(defaultZoom.value * 100 + zoomStep * 100) / 100
    if (newZoom > 4) {
      throw new Error('[Process Viewer Warn]: The zoom ratio cannot be greater than 4')
    }
    defaultZoom.value = newZoom
    bpmnModeler.get('canvas').zoom(defaultZoom.value)
  }

  const processZoomOut = (zoomStep = 0.1) => {
    let newZoom = Math.floor(defaultZoom.value * 100 - zoomStep * 100) / 100
    if (newZoom < 0.2) {
      throw new Error('[Process Viewer Warn]: The zoom ratio cannot be less than 0.2')
    }
    defaultZoom.value = newZoom
    bpmnModeler.get('canvas').zoom(defaultZoom.value)
  }

  const processReZoom = () => {
    if (isMobileDevice.value) {
      bpmnModeler.get('canvas').zoom(0.5)
      defaultZoom.value = 0.5
    } else {
      bpmnModeler.get('canvas').zoom(1)
      defaultZoom.value = 1
    }
  }

  // 初始化拖动功能
  const initDraggingOld = () => {
    const canvas = bpmnModeler.get('canvas')
    const eventBus = bpmnModeler.get('eventBus')
    let isDragging = false
    let lastX, lastY

    // 鼠标/触摸移动处理函数
    const handleMove = (clientX, clientY) => {
      if (isDragging) {
        const dx = clientX - lastX
        const dy = clientY - lastY
        canvas.scroll({ dx, dy })
        lastX = clientX
        lastY = clientY
      }
    }

    // 鼠标/触摸结束处理函数
    const handleEnd = () => {
      isDragging = false
    }

    eventBus.on('element.mousedown', event => {
      isDragging = true
      lastX = event.originalEvent.clientX
      lastY = event.originalEvent.clientY
    })

    // 根据平台使用不同的事件监听方式

    if (true) {
      // H5 端使用鼠标事件
      const mouseMoveHandler = event => handleMove(event.clientX, event.clientY)
      document.addEventListener('mousemove', mouseMoveHandler)
      document.addEventListener('mouseup', handleEnd)

      // 组件卸载时移除事件监听
      onBeforeUnmount(() => {
        document.removeEventListener('mousemove', mouseMoveHandler)
        document.removeEventListener('mouseup', handleEnd)
      })
    } else {
      // 非 H5 端使用触摸事件
      const touchMoveHandler = (event: TouchEvent) => {
        const touch = event.touches[0]
        handleMove(touch.clientX, touch.clientY)
      }

      const touchEndHandler = () => handleEnd()

      // 添加触摸事件监听
      const canvas = bpmnCanvas.value
      canvas.addEventListener('touchmove', touchMoveHandler)
      canvas.addEventListener('touchend', touchEndHandler)

      // 组件卸载时移除事件监听
      onBeforeUnmount(() => {
        canvas.removeEventListener('touchmove', touchMoveHandler)
        canvas.removeEventListener('touchend', touchEndHandler)
      })
    }
  }

  // 初始化拖动功能
  const initDragging = () => {
    const canvas = bpmnModeler.get('canvas')
    const eventBus = bpmnModeler.get('eventBus')
    let isDragging = false
    let lastX, lastY

    eventBus.on('element.mousedown', event => {
      isDragging = true
      lastX = event.originalEvent.clientX
      lastY = event.originalEvent.clientY
    })

    document.addEventListener('mousemove', event => {
      if (isDragging) {
        const dx = event.clientX - lastX
        const dy = event.clientY - lastY
        canvas.scroll({ dx, dy })
        lastX = event.clientX
        lastY = event.clientY
      }
    })

    document.addEventListener('mouseup', () => {
      isDragging = false
    })
  }

  // 下载流程图功能
  const downloadDiagram = () => {
    // 获取当前 BPMN 实例
    if (!bpmnModeler) return

    // 下载 SVG 图片
    bpmnModeler.saveSVG((err: any, svg: string) => {
      if (err) {
        console.error('导出 SVG 失败', err)
        return
      }

      // 创建下载链接
      const name = '流程图_' + new Date().getTime()
      const blob = new Blob([svg], { type: 'image/svg+xml;charset=UTF-8' })
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = name + '.svg'
      document.body.appendChild(link)
      // link.click()

      // 清理
      document.body.removeChild(link)

      // 将 SVG 转换为 PNG 并下载
      convertSvgToPng(svg, name)
    })
  }

  // SVG 转换为 PNG
  const convertSvgToPng = (svgData: string, fileName: string) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      // 设置合适的图片大小
      canvas.width = img.width
      canvas.height = img.height

      // 绘制图像到 canvas
      ctx.drawImage(img, 0, 0)

      // 转换为 PNG
      try {
        const pngUrl = canvas.toDataURL('image/png')
        const link = document.createElement('a')
        link.href = pngUrl
        link.download = fileName + '.png'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } catch (e) {
        console.error('PNG 导出失败', e)
      }
    }

    // 转换 SVG 为 DataURL
    const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' })
    const url = URL.createObjectURL(svgBlob)
    img.src = url
  }

  onMounted(() => {
    init()
  })
  const init = () => {
    xml.value = props.value
    activityLists.value = props.activityData
    // 初始化
    initBpmnModeler()
    createNewDiagram(xml.value)
    // 初始模型的监听器
    initModelListeners()
    initDragging()
  }

  onBeforeUnmount(() => {
    // this.$once('hook:beforeDestroy', () => {
    // })
    if (bpmnModeler) bpmnModeler.destroy()
    emit('destroy', bpmnModeler)
    bpmnModeler = null
  })

  watch(
    () => props.value,
    newValue => {
      xml.value = newValue
      createNewDiagram(xml.value)
    }
  )
  watch(
    () => props.activityData,
    newActivityData => {
      activityLists.value = newActivityData
      createNewDiagram(xml.value)
    }
  )
  watch(
    () => props.processInstanceData,
    newProcessInstanceData => {
      processInstance.value = newProcessInstanceData
      createNewDiagram(xml.value)
    }
  )
  watch(
    () => props.taskData,
    newTaskListData => {
      taskList.value = newTaskListData
      createNewDiagram(xml.value)
    }
  )
</script>

<style lang="scss">
  /** 处理中 */
  .highlight-todo.djs-connection > .djs-visual > path {
    stroke: #1890ff !important;
    stroke-dasharray: 4px !important;
    fill-opacity: 0.2 !important;
  }

  .highlight-todo.djs-shape .djs-visual > :nth-child(1) {
    fill: #1890ff !important;
    stroke: #1890ff !important;
    stroke-dasharray: 4px !important;
    fill-opacity: 0.2 !important;
  }

  :deep(.highlight-todo.djs-connection > .djs-visual > path) {
    stroke: #1890ff !important;
    stroke-dasharray: 4px !important;
    fill-opacity: 0.2 !important;
    marker-end: url('#sequenceflow-end-_E7DFDF-_E7DFDF-803g1kf6zwzmcig1y2ulm5egr');
  }

  :deep(.highlight-todo.djs-shape .djs-visual > :nth-child(1)) {
    fill: #1890ff !important;
    stroke: #1890ff !important;
    stroke-dasharray: 4px !important;
    fill-opacity: 0.2 !important;
  }

  /** 通过 */
  .highlight.djs-shape .djs-visual > :nth-child(1) {
    fill: green !important;
    stroke: green !important;
    fill-opacity: 0.2 !important;
  }

  .highlight.djs-shape .djs-visual > :nth-child(2) {
    fill: green !important;
  }

  .highlight.djs-shape .djs-visual > path {
    fill: green !important;
    fill-opacity: 0.2 !important;
    stroke: green !important;
  }

  .highlight.djs-connection > .djs-visual > path {
    stroke: green !important;
  }

  .highlight:not(.djs-connection) .djs-visual > :nth-child(1) {
    fill: green !important; /* color elements as green */
  }

  :deep(.highlight.djs-shape .djs-visual > :nth-child(1)) {
    fill: green !important;
    stroke: green !important;
    fill-opacity: 0.2 !important;
  }

  :deep(.highlight.djs-shape .djs-visual > :nth-child(2)) {
    fill: green !important;
  }

  :deep(.highlight.djs-shape .djs-visual > path) {
    fill: green !important;
    fill-opacity: 0.2 !important;
    stroke: green !important;
  }

  :deep(.highlight.djs-connection > .djs-visual > path) {
    stroke: green !important;
  }

  .djs-element.highlight > .djs-visual > path {
    stroke: green !important;
  }

  /** 不通过 */
  .highlight-reject.djs-shape .djs-visual > :nth-child(1) {
    fill: red !important;
    stroke: red !important;
    fill-opacity: 0.2 !important;
  }

  .highlight-reject.djs-shape .djs-visual > :nth-child(2) {
    fill: red !important;
  }

  .highlight-reject.djs-shape .djs-visual > path {
    fill: red !important;
    fill-opacity: 0.2 !important;
    stroke: red !important;
  }

  .highlight-reject.djs-connection > .djs-visual > path {
    stroke: red !important;
    marker-end: url(#sequenceflow-end-white-success) !important;
  }

  .highlight-reject:not(.djs-connection) .djs-visual > :nth-child(1) {
    fill: red !important; /* color elements as green */
  }

  :deep(.highlight-reject.djs-shape .djs-visual > :nth-child(1)) {
    fill: red !important;
    stroke: red !important;
    fill-opacity: 0.2 !important;
  }

  :deep(.highlight-reject.djs-shape .djs-visual > :nth-child(2)) {
    fill: red !important;
  }

  :deep(.highlight-reject.djs-shape .djs-visual > path) {
    fill: red !important;
    fill-opacity: 0.2 !important;
    stroke: red !important;
  }

  :deep(.highlight-reject.djs-connection > .djs-visual > path) {
    stroke: red !important;
  }

  /** 已取消 */
  .highlight-cancel.djs-shape .djs-visual > :nth-child(1) {
    fill: grey !important;
    stroke: grey !important;
    fill-opacity: 0.2 !important;
  }

  .highlight-cancel.djs-shape .djs-visual > :nth-child(2) {
    fill: grey !important;
  }

  .highlight-cancel.djs-shape .djs-visual > path {
    fill: grey !important;
    fill-opacity: 0.2 !important;
    stroke: grey !important;
  }

  .highlight-cancel.djs-connection > .djs-visual > path {
    stroke: grey !important;
  }

  .highlight-cancel:not(.djs-connection) .djs-visual > :nth-child(1) {
    fill: grey !important; /* color elements as green */
  }

  :deep(.highlight-cancel.djs-shape .djs-visual > :nth-child(1)) {
    fill: grey !important;
    stroke: grey !important;
    fill-opacity: 0.2 !important;
  }

  :deep(.highlight-cancel.djs-shape .djs-visual > :nth-child(2)) {
    fill: grey !important;
  }

  :deep(.highlight-cancel.djs-shape .djs-visual > path) {
    fill: grey !important;
    fill-opacity: 0.2 !important;
    stroke: grey !important;
  }

  :deep(.highlight-cancel.djs-connection > .djs-visual > path) {
    stroke: grey !important;
  }

  /** 回退 */
  .highlight-return.djs-shape .djs-visual > :nth-child(1) {
    fill: #e6a23c !important;
    stroke: #e6a23c !important;
    fill-opacity: 0.2 !important;
  }

  .highlight-return.djs-shape .djs-visual > :nth-child(2) {
    fill: #e6a23c !important;
  }

  .highlight-return.djs-shape .djs-visual > path {
    fill: #e6a23c !important;
    fill-opacity: 0.2 !important;
    stroke: #e6a23c !important;
  }

  .highlight-return.djs-connection > .djs-visual > path {
    stroke: #e6a23c !important;
  }

  .highlight-return:not(.djs-connection) .djs-visual > :nth-child(1) {
    fill: #e6a23c !important; /* color elements as green */
  }

  :deep(.highlight-return.djs-shape .djs-visual > :nth-child(1)) {
    fill: #e6a23c !important;
    stroke: #e6a23c !important;
    fill-opacity: 0.2 !important;
  }

  :deep(.highlight-return.djs-shape .djs-visual > :nth-child(2)) {
    fill: #e6a23c !important;
  }

  :deep(.highlight-return.djs-shape .djs-visual > path) {
    fill: #e6a23c !important;
    fill-opacity: 0.2 !important;
    stroke: #e6a23c !important;
  }

  :deep(.highlight-return.djs-connection > .djs-visual > path) {
    stroke: #e6a23c !important;
  }

  .element-overlays {
    min-width: 320px;
    max-width: 520px;
    padding: 8px 10px;
    color: #333;
    margin-right: -100px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    font-size: 13px;
    line-height: 1.5;
    transition: all 0.3s ease;
    font-family: 'Helvetica Neue', Arial, sans-serif;
    letter-spacing: 0.3px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
    border-color: #d0d0d0;

    p {
      margin-bottom: 2px;

      &:last-child {
        margin-bottom: 0;
      }

      > svg {
        width: 1em !important;
        height: 1em !important;
        margin-right: 2px;
        vertical-align: middle;
      }
    }

    .approver-info {
      padding: 4px 0;

      p {
        padding-left: 4px;
      }
    }

    .node-id {
      margin-top: 5px;
      border-top: 1px dotted #eee;
      padding-top: 5px;
      color: #999;
      font-size: 12px;
    }
  }

  /* 多签任务分隔线样式 */
  .element-overlays div[style*='border-top'] {
    margin: 10px 0 !important;
    border-top: 1px dashed #aaa !important;
  }

  /* 多签类型样式 */
  .multi-sign-type {
    margin-bottom: 10px;
    padding: 8px 10px;
    border-radius: 6px;
    background-color: #f5f7fa;
  }

  .multi-sign-type .sign-type-label {
    font-weight: bold;
  }

  .multi-sign-type .sign-progress {
    font-weight: bold;
  }

  .multi-sign-type.sequential {
    background-color: #e6f7ff;
    border-left: 3px solid #1890ff;
  }

  .multi-sign-type.and {
    background-color: #f6ffed;
    border-left: 3px solid #52c41a;
  }

  .multi-sign-type.or {
    background-color: #fff7e6;
    border-left: 3px solid #fa8c16;
  }

  .multi-sign-type.parallel {
    background-color: #f9f0ff;
    border-left: 3px solid #722ed1;
  }

  /* 流程图中多签类型节点高亮样式 */
  .multi-sequential.djs-shape .djs-visual > :nth-child(1) {
    stroke-dasharray: 4px !important;
    stroke-width: 2px !important;
    stroke: #1890ff !important;
  }

  .multi-and.djs-shape .djs-visual > :nth-child(1) {
    stroke-dasharray: 0 !important;
    stroke-width: 2px !important;
    stroke: #52c41a !important;
  }

  .multi-or.djs-shape .djs-visual > :nth-child(1) {
    stroke-dasharray: 6px !important;
    stroke-width: 2px !important;
    stroke: #fa8c16 !important;
  }

  .multi-parallel.djs-shape .djs-visual > :nth-child(1) {
    stroke-dasharray: 2px !important;
    stroke-width: 2px !important;
    stroke: #722ed1 !important;
  }

  /* 同时支持深层嵌套的multi标记 */
  :deep(.multi-sequential.djs-shape .djs-visual > :nth-child(1)) {
    stroke-dasharray: 4px !important;
    stroke-width: 2px !important;
    stroke: #1890ff !important;
  }

  :deep(.multi-and.djs-shape .djs-visual > :nth-child(1)) {
    stroke-dasharray: 0 !important;
    stroke-width: 2px !important;
    stroke: #52c41a !important;
  }

  :deep(.multi-or.djs-shape .djs-visual > :nth-child(1)) {
    stroke-dasharray: 6px !important;
    stroke-width: 2px !important;
    stroke: #fa8c16 !important;
  }

  :deep(.multi-parallel.djs-shape .djs-visual > :nth-child(1)) {
    stroke-dasharray: 2px !important;
    stroke-width: 2px !important;
    stroke: #722ed1 !important;
  }

  .preview-icon {
    width: 1em !important;
    height: 1em !important;
  }

  .my-process-designer {
    position: relative;
    // position: fixed;
    // top: 0;
    // left: 0;
    // width: 100vh;
    // height: 100vw;
    // transform: rotate(90deg);
    // transform-origin: center;
    // margin-left: calc((100vw - 100vh) / 2);
    // margin-top: calc((100vh - 100vw) / 2);
  }

  .zoom-controls {
    position: absolute;
    bottom: 20px;
    right: 20px;
    display: flex;
    gap: 8px;
    padding: 10px;
    background-color: rgba(250, 250, 252, 0.85);
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(4px);
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 3px 12px rgba(0, 0, 0, 0.12);
    }

    .zoom-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &.naive-style {
        border-radius: 3px;
        font-size: 13px;
        border: 1px solid #e0e0e0;

        &:hover {
          color: #18a058;
          border-color: #18a058;
          background-color: rgba(24, 160, 88, 0.1);
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
        }

        &[type='success'] {
          background-color: #18a058;
          color: white;
          border-color: #18a058;

          &:hover {
            background-color: #36ad6a;
            border-color: #36ad6a;
          }
        }

        &[type='info'] {
          background-color: #2080f0;
          color: white;
          border-color: #2080f0;

          &:hover {
            background-color: #4098fc;
            border-color: #4098fc;
          }
        }
      }

      .zoom-icon {
        margin-right: 4px;
      }
    }

    .zoom-value {
      min-width: 60px;
      font-weight: bold;
      background-color: transparent;
      border: 1px solid #e0e0e0;
      color: #666;
    }
  }

  /* 多签类型图例样式 */
  .multi-sign-legend {
    position: absolute;
    bottom: 20px;
    left: 20px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(4px);
    border: 1px solid #e0e0e0;
    font-size: 13px;
    z-index: 100;
  }

  .legend-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .legend-close {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    transition: all 0.2s;

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }

    svg {
      color: #999;
    }
  }

  .legend-title {
    font-weight: bold;
    margin-bottom: 5px;
  }

  .legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .legend-icon {
    width: 20px;
    height: 20px;
    border-radius: 3px;
    border: 1px solid #ddd;
  }

  .legend-item.sequential .legend-icon {
    border: 2px dashed #1890ff;
  }

  .legend-item.and .legend-icon {
    border: 2px solid #52c41a;
  }

  .legend-item.or .legend-icon {
    border: 2px dotted #fa8c16;
  }

  .legend-item.parallel .legend-icon {
    border: 2px dashed #722ed1;
    border-style: dashed double;
  }
</style>
